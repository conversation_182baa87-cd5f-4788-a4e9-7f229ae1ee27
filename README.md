# Pyramid

基于[PyramidStore](https://github.com/UndCover/PyramidStore)，用于支持影视及其衍生app使用python爬虫作为数据源，[原版Pyramid源码地址](https://github.com/UndCover/Pyramid)

## 调试示例

参考 [小白调试示例.py](https://github.com/JJBJJ/PyramidStore/tree/main/plugin/小白调试示例.py)

## 免责声明

本项目仅供爬虫技术学习交流使用，所有代码开源且免费，严禁任何商业用途，搜索结果均来自源站，本人不承担任何责任

## 食用方法

推荐：ok影视

开袋即食：不需要挂载任何jar！不需要挂载任何jar！不需要挂载任何jar！！！
**一定要开启存储权限！一定要开启存储权限！一定要开启存储权限！**

## 配置示例

配置文件sites添加内容参考 [example.json](https://github.com/JJBJJ/PyramidStore/blob/main/example.json)

### [Python爬虫写法参考](https://github.com/JJBJJ/PyramidStore/blob/main/spider.md)

### [影视版源码地址](https://github.com/FongMi/TV/tree/release/chaquo)

### 问题反馈
问题请反馈到[telegram](https://t.me/+A3SLQRmPVi9kOThl)
