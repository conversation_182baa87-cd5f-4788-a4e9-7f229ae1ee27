# -*- coding: utf-8 -*-
# 播剧网爬虫 - https://www.boju.cc/
# 作者: AI Assistant
# 说明: 基于PyramidStore框架的影视爬虫，参考统一影视精简版优化

import sys
import re
import json
import urllib.parse
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from base.spider import Spider


class Spider(Spider):

    def init(self, extend=""):
        self.host = 'https://www.boju.cc'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def getName(self):
        return "播剧网"

    def _extract_video_info(self, element):
        """提取视频信息"""
        try:
            # 获取视频链接和ID - 更新选择器
            link_elem = element.xpath('.//a[contains(@href,"/voddetail/")]/@href')
            if not link_elem:
                # 尝试其他可能的选择器
                link_elem = element.xpath('.//a/@href')
                if link_elem:
                    link_elem = [link for link in link_elem if '/voddetail/' in link]
                if not link_elem:
                    return None

            link = link_elem[0]
            vod_id = self.regStr(r'/voddetail/(\d+)\.html', link)
            if not vod_id:
                return None

            # 获取标题 - 更新选择器
            title_selectors = [
                './/a[contains(@href,"/voddetail/")]/@title',
                './/a[contains(@href,"/voddetail/")]/text()',
                './/h3//text()',
                './/h4//text()',
                './/div[@class="video-name"]/text()',
                './/span[@class="video-name"]/text()'
            ]

            title = ''
            for title_selector in title_selectors:
                title_elem = element.xpath(title_selector)
                if title_elem:
                    title = title_elem[0].strip()
                    if title and len(title) > 1:
                        break

            # 获取封面图片 - 更新选择器
            pic_elem = element.xpath('.//img/@data-src | .//img/@src | .//img/@data-original')
            pic = ''
            for pic_url in pic_elem:
                if pic_url and not pic_url.startswith('data:') and 'load' not in pic_url:
                    pic = pic_url
                    if not pic.startswith('http'):
                        pic = self.host + pic
                    break

            # 获取更新状态 - 更新选择器
            remarks_selectors = [
                './/span[contains(@class,"pic-text")]/text()',
                './/div[contains(@class,"pic-text")]/text()',
                './/span[contains(text(),"更新")]/text()',
                './/span[contains(text(),"集")]/text()',
                './/span[contains(text(),"完结")]/text()',
                './/div[contains(@class,"video-serial")]/text()',
                './/span[contains(@class,"video-serial")]/text()'
            ]

            remarks = ''
            for remarks_selector in remarks_selectors:
                remarks_elem = element.xpath(remarks_selector)
                if remarks_elem:
                    remarks = remarks_elem[0].strip()
                    if remarks:
                        break

            if title and vod_id:
                return {
                    'vod_id': vod_id,
                    'vod_name': title,
                    'vod_pic': pic,
                    'vod_remarks': remarks,
                    'vod_year': ''
                }
        except Exception as e:
            self.log(f"解析视频信息出错: {str(e)}")
        return None

    def homeContent(self, filter):
        """获取首页内容和分类"""
        result = {}
        
        # 定义分类
        classes = [
            {'type_name': '电影', 'type_id': 'movie'},
            {'type_name': '连续剧', 'type_id': 'tvplay'},
            {'type_name': '综艺', 'type_id': 'tvshow'},
            {'type_name': '动漫', 'type_id': 'dongman'},
            {'type_name': '短剧', 'type_id': 'duanju'},
        ]
        result['class'] = classes
        
        # 获取首页推荐内容 - 优化版
        try:
            rsp = self.fetch(self.host, headers=self.headers)
            content = rsp.text
            doc = self.html(content)

            # 尝试多种选择器提取推荐视频
            video_selectors = [
                '//div[contains(@class,"module-item")]',
                '//li[contains(@class,"module-item")]',
                '//div[contains(@class,"video-item")]',
                '//li[contains(@class,"video-item")]',
                '//div[@class="module-item"]',
                '//a[contains(@href,"/voddetail/")]/..'
            ]

            videos = []
            for selector in video_selectors:
                video_elements = doc.xpath(selector)
                self.log(f"首页使用选择器 {selector} 找到 {len(video_elements)} 个元素")

                if video_elements:
                    temp_videos = []
                    for element in video_elements:
                        video_info = self._extract_video_info(element)
                        if video_info:
                            temp_videos.append(video_info)

                    if temp_videos:
                        videos = temp_videos
                        break

            # 限制首页视频数量，提高性能
            if len(videos) > 50:
                videos = videos[:50]

            # 去重处理
            videos = self._remove_duplicates(videos)

            result['list'] = videos

        except Exception as e:
            self.log(f"获取首页内容出错: {str(e)}")
            result['list'] = []
        
        return result

    def categoryContent(self, tid, pg, filter, extend):
        """获取分类内容"""
        result = {}
        videos = []
        
        try:
            # 构建分类页面URL - 修复正确的URL格式
            if pg == '1':
                url = f"{self.host}/vodtype/{tid}.html"
            else:
                url = f"{self.host}/vodtype/{tid}.html?page={pg}"

            self.log(f"分类页面URL: {url}")

            rsp = self.fetch(url, headers=self.headers)
            content = rsp.text
            doc = self.html(content)

            # 尝试多种选择器解析视频列表
            video_selectors = [
                '//div[contains(@class,"module-item")]',
                '//li[contains(@class,"module-item")]',
                '//div[contains(@class,"video-item")]',
                '//li[contains(@class,"video-item")]',
                '//div[@class="module-item"]',
                '//a[contains(@href,"/voddetail/")]/..'
            ]

            temp_videos = []
            for selector in video_selectors:
                video_elements = doc.xpath(selector)
                self.log(f"分类页使用选择器 {selector} 找到 {len(video_elements)} 个元素")

                if video_elements:
                    for element in video_elements:
                        video_info = self._extract_video_info(element)
                        if video_info:
                            temp_videos.append(video_info)

                    if temp_videos:  # 如果找到了视频，就停止尝试其他选择器
                        videos = temp_videos
                        break

            # 去重处理
            videos = self._remove_duplicates(videos)
            
            result['list'] = videos
            result['page'] = pg
            result['pagecount'] = 999
            result['limit'] = 20
            result['total'] = 999 * 20
            
        except Exception as e:
            self.log(f"获取分类内容出错: {str(e)}")
            result['list'] = []
            result['page'] = pg
            result['pagecount'] = 1
            result['limit'] = 20
            result['total'] = 0
        
        return result

    def searchContent(self, key, quick, pg="1"):
        """搜索内容 - 优化版"""
        result = {}
        videos = []

        try:
            # 构建搜索URL - 支持分页
            if pg == "1":
                search_url = f"{self.host}/vodsearch/{urllib.parse.quote(key)}-------------.html"
            else:
                search_url = f"{self.host}/vodsearch/{urllib.parse.quote(key)}-------------.html?page={pg}"

            rsp = self.fetch(search_url, headers=self.headers)
            content = rsp.text
            doc = self.html(content)

            # 尝试多种选择器解析搜索结果
            search_selectors = [
                '//div[contains(@class,"module-search-item")]',
                '//div[contains(@class,"search-item")]',
                '//li[contains(@class,"search-item")]',
                '//div[contains(@class,"module-item")]',
                '//div[contains(@class,"video-item")]',
                '//a[contains(@href,"/voddetail/")]/..'
            ]

            search_elements = []
            for selector in search_selectors:
                elements = doc.xpath(selector)
                self.log(f"搜索页使用选择器 {selector} 找到 {len(elements)} 个元素")
                if elements:
                    search_elements = elements
                    break

            self.log(f"最终找到 {len(search_elements)} 个搜索结果")

            # 过滤和处理搜索结果
            for element in search_elements:
                try:
                    video_info = self._extract_video_info(element)
                    if video_info and self._is_valid_search_result(video_info, key):
                        videos.append(video_info)

                except Exception as e:
                    continue

            # 去重处理
            videos = self._remove_duplicates(videos)

            result['list'] = videos

        except Exception as e:
            self.log(f"搜索出错: {str(e)}")
            result['list'] = []

        return result

    def _is_valid_search_result(self, video_info, search_key):
        """验证搜索结果是否有效"""
        if not video_info or not video_info.get('vod_name'):
            return False

        # 检查标题长度
        if len(video_info['vod_name']) < 2:
            return False

        # 检查是否包含搜索关键词（可选）
        # 这里可以根据需要调整匹配策略
        return True

    def _remove_duplicates(self, videos):
        """去除重复的视频"""
        seen_ids = set()
        unique_videos = []

        for video in videos:
            vod_id = video.get('vod_id')
            if vod_id and vod_id not in seen_ids:
                seen_ids.add(vod_id)
                unique_videos.append(video)

        return unique_videos

    def detailContent(self, ids):
        """获取详情内容"""
        result = {}
        videos = []

        try:
            for vod_id in ids:
                detail_url = f"{self.host}/voddetail/{vod_id}.html"
                rsp = self.fetch(detail_url, headers=self.headers)
                content = rsp.text
                doc = self.html(content)

                # 获取基本信息 - 更新选择器
                title_selectors = [
                    '//h1/text()',
                    '//div[@class="video-info-title"]/h1/text()',
                    '//h3/text()',
                    '//title/text()',
                    '//div[contains(@class,"title")]/text()'
                ]

                title = ''
                for selector in title_selectors:
                    title_elem = doc.xpath(selector)
                    if title_elem:
                        title = title_elem[0].strip()
                        # 清理标题中的网站名称
                        if '播剧网' in title:
                            title = title.split('_')[0].strip()
                        if title and len(title) > 1:
                            break

                # 获取封面图片
                pic_selectors = [
                    '//div[@class="video-info-pic"]//img/@src',
                    '//img[@class="lazyload"]/@data-src',
                    '//img/@data-src',
                    '//img/@src'
                ]

                pic = ''
                for selector in pic_selectors:
                    pic_elem = doc.xpath(selector)
                    if pic_elem:
                        for pic_url in pic_elem:
                            if pic_url and not pic_url.startswith('data:') and 'load' not in pic_url:
                                pic = pic_url
                                if not pic.startswith('http'):
                                    pic = self.host + pic
                                break
                        if pic:
                            break

                # 获取描述信息
                desc_selectors = [
                    '//div[@class="video-info-content"]/text()',
                    '//div[contains(@class,"desc")]/text()',
                    '//div[contains(@class,"content")]/text()',
                    '//p[contains(@class,"desc")]/text()'
                ]

                desc = ''
                for selector in desc_selectors:
                    desc_elem = doc.xpath(selector)
                    if desc_elem:
                        desc = desc_elem[0].strip()
                        if desc and len(desc) > 10:
                            break

                # 获取演员、导演等信息 - 优化版
                director = ''
                actor = ''
                year = ''
                area = ''

                # 从页面文本中提取信息 - 更准确的方法
                page_text = doc.xpath('//text()')
                all_text = ' '.join([text.strip() for text in page_text if text.strip()])

                # 优化的导演信息提取
                director_patterns = [
                    r'导演[：:]\s*([^主演年份地区语言类型更新简介\n]+)',
                    r'导演[：:]\s*([^\n]+)',
                    r'导演\s*([^主演年份地区语言类型更新简介\n]+)',
                ]
                for pattern in director_patterns:
                    match = re.search(pattern, all_text)
                    if match:
                        director = match.group(1).strip()
                        # 清理导演信息
                        director = re.sub(r'[,，]\s*$', '', director)  # 移除末尾逗号
                        director = re.sub(r'\s+', ' ', director)  # 合并多个空格
                        if director and len(director) > 1:
                            break

                # 优化的演员信息提取
                actor_patterns = [
                    r'主演[：:]\s*([^导演年份地区语言类型更新简介\n]+)',
                    r'演员[：:]\s*([^导演年份地区语言类型更新简介\n]+)',
                    r'主演\s*([^导演年份地区语言类型更新简介\n]+)',
                ]
                for pattern in actor_patterns:
                    match = re.search(pattern, all_text)
                    if match:
                        actor = match.group(1).strip()
                        # 清理演员信息
                        actor = re.sub(r'[,，]\s*$', '', actor)  # 移除末尾逗号
                        actor = re.sub(r'\s+', ' ', actor)  # 合并多个空格
                        if actor and len(actor) > 1:
                            break

                # 提取年份信息
                year_patterns = [
                    r'年份[：:]\s*(\d{4})',
                    r'(\d{4})年',
                    r'上映[：:]\s*(\d{4})',
                ]
                for pattern in year_patterns:
                    match = re.search(pattern, all_text)
                    if match:
                        year = match.group(1)
                        break

                # 提取地区信息
                area_patterns = [
                    r'地区[：:]\s*([^导演主演年份语言类型更新简介\n]+)',
                    r'(美国|中国|日本|韩国|英国|法国|德国|意大利|加拿大|澳大利亚|大陆|香港|台湾|泰国|印度)',
                ]
                for pattern in area_patterns:
                    match = re.search(pattern, all_text)
                    if match:
                        area = match.group(1).strip()
                        if area and len(area) > 1:
                            break

                # 获取播放列表
                play_from, play_url = self._parse_play_sources(doc, vod_id)

                video_info = {
                    'vod_id': vod_id,
                    'vod_name': title,
                    'vod_pic': pic,
                    'vod_remarks': '',
                    'vod_year': year,
                    'vod_area': area,
                    'vod_director': director,
                    'vod_actor': actor,
                    'vod_content': desc,
                    'vod_play_from': '$$$'.join(play_from),
                    'vod_play_url': '$$$'.join(play_url)
                }
                videos.append(video_info)

            result['list'] = videos

        except Exception as e:
            self.log(f"获取详情出错: {str(e)}")
            result['list'] = []

        return result

    def _parse_play_sources(self, doc, vod_id):
        """解析播放源 - 优化版"""
        play_from = []
        play_url = []

        try:
            # 多种选择器尝试查找播放源标题
            source_title_selectors = [
                '//div[@class="module-tab-item tab-item"]/span/text()',
                '//h3[contains(@class,"title")]/text()',
                '//div[contains(@class,"tab-item")]/text()',
                '//span[contains(@class,"tab-title")]/text()',
                '//a[contains(@class,"tab")]/text()'
            ]

            source_titles = []
            for selector in source_title_selectors:
                titles = doc.xpath(selector)
                if titles:
                    source_titles = [title.strip() for title in titles if title.strip()]
                    break

            # 多种选择器尝试查找播放链接列表
            play_list_selectors = [
                '//div[@class="module-play-list"]',
                '//div[contains(@class,"play-list")]',
                '//ul[contains(@class,"play-list")]',
                '//div[contains(@class,"episode-list")]'
            ]

            play_lists = []
            for selector in play_list_selectors:
                lists = doc.xpath(selector)
                if lists:
                    play_lists = lists
                    break

            # 如果没有找到播放源标题，使用默认名称
            if not source_titles:
                source_titles = ['播剧蓝光']

            # 如果找到了播放列表，解析每个播放源
            if play_lists:
                for i, play_list in enumerate(play_lists):
                    # 获取播放源名称
                    source_name = source_titles[i] if i < len(source_titles) else f'播放源{i+1}'

                    # 清理播放源名称
                    source_name = self._clean_source_name(source_name)

                    # 获取播放链接
                    episode_selectors = [
                        './/a[contains(@href,"/vodplay/")]',
                        './/a',
                        './/li/a'
                    ]

                    episodes = []
                    for ep_selector in episode_selectors:
                        episodes = play_list.xpath(ep_selector)
                        if episodes:
                            break

                    episode_list = []
                    for ep in episodes:
                        ep_title = ep.xpath('./text()')[0] if ep.xpath('./text()') else ''
                        ep_url = ep.xpath('./@href')[0] if ep.xpath('./@href') else ''

                        if ep_title and ep_url and '/vodplay/' in ep_url:
                            # 确保URL格式正确
                            if not ep_url.startswith('http'):
                                ep_url = ep_url if ep_url.startswith('/') else '/' + ep_url
                            episode_list.append(f"{ep_title.strip()}${ep_url}")

                    if episode_list:
                        play_from.append(source_name)
                        play_url.append('#'.join(episode_list))

            # 如果没有找到任何播放源，尝试直接查找所有播放链接
            if not play_from:
                all_episodes = doc.xpath('//a[contains(@href,"/vodplay/")]')
                if all_episodes:
                    episode_list = []
                    for ep in all_episodes:
                        ep_title = ep.xpath('./text()')[0] if ep.xpath('./text()') else ''
                        ep_url = ep.xpath('./@href')[0] if ep.xpath('./@href') else ''

                        if ep_title and ep_url:
                            if not ep_url.startswith('http'):
                                ep_url = ep_url if ep_url.startswith('/') else '/' + ep_url
                            episode_list.append(f"{ep_title.strip()}${ep_url}")

                    if episode_list:
                        play_from.append('播剧蓝光')
                        play_url.append('#'.join(episode_list))

            # 如果仍然没有找到播放源，创建默认播放源
            if not play_from:
                play_from.append('播剧蓝光')
                play_url.append(f'第1集$/vodplay/{vod_id}-1-1.html')

        except Exception as e:
            self.log(f"解析播放源出错: {str(e)}")
            play_from = ['播剧蓝光']
            play_url = [f'第1集$/vodplay/{vod_id}-1-1.html']

        return play_from, play_url

    def _clean_source_name(self, source_name):
        """清理播放源名称"""
        if not source_name:
            return '播剧蓝光'

        # 移除多余的空格和特殊字符
        source_name = re.sub(r'\s+', ' ', source_name.strip())

        # 如果包含"播剧"，保持原名
        if '播剧' in source_name:
            return source_name

        # 如果是数字，添加前缀
        if source_name.isdigit():
            return f'播放源{source_name}'

        # 如果为空或太短，使用默认名称
        if len(source_name) < 2:
            return '播剧蓝光'

        return source_name

    def _extract_video_url(self, content):
        """提取视频URL"""
        # 常见的视频URL模式
        patterns = [
            r'"url":\s*"([^"]*\.m3u8[^"]*)"',
            r'"url":\s*"([^"]*\.mp4[^"]*)"',
            r'src\s*=\s*["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'src\s*=\s*["\']([^"\']*\.mp4[^"\']*)["\']',
            r'video_url\s*=\s*["\']([^"\']*)["\']',
            r'playurl\s*=\s*["\']([^"\']*)["\']',
            r'var\s+url\s*=\s*["\']([^"\']*)["\']'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if match and ('m3u8' in match or 'mp4' in match):
                    # 处理转义字符和解码URL
                    try:
                        # 处理JSON转义字符
                        clean_url = match.replace('\\/', '/')
                        # URL解码
                        decoded_url = urllib.parse.unquote(clean_url)
                        if decoded_url.startswith('http'):
                            return decoded_url
                    except:
                        # 如果解码失败，尝试直接处理转义字符
                        clean_url = match.replace('\\/', '/')
                        if clean_url.startswith('http'):
                            return clean_url
        return ''

    def playerContent(self, flag, id, vipFlags):
        """获取播放链接"""
        result = {}

        try:
            # 构建播放页面URL
            play_url = self.host + id if id.startswith('/vodplay/') else self.host + id
            rsp = self.fetch(play_url, headers=self.headers)
            content = rsp.text

            # 查找iframe中的视频链接
            iframe_pattern = r'<iframe[^>]*src=["\']([^"\']*)["\'][^>]*>'
            iframe_matches = re.findall(iframe_pattern, content, re.IGNORECASE)

            for iframe_url in iframe_matches:
                if iframe_url and ('player' in iframe_url.lower() or 'play' in iframe_url.lower()):
                    if not iframe_url.startswith('http'):
                        iframe_url = urllib.parse.urljoin(self.host, iframe_url)

                    try:
                        iframe_rsp = self.fetch(iframe_url, headers=self.headers)
                        iframe_content = iframe_rsp.text

                        video_url = self._extract_video_url(iframe_content)
                        if video_url:
                            result['parse'] = 0
                            result['playUrl'] = ''
                            result['url'] = video_url
                            result['header'] = {}
                            return result
                    except:
                        continue

            # 直接在页面中查找视频URL
            video_url = self._extract_video_url(content)
            if video_url:
                result['parse'] = 0
                result['playUrl'] = ''
                result['url'] = video_url
                result['header'] = {}
                return result

        except Exception as e:
            self.log(f"获取播放链接出错: {str(e)}")

        return result

    def isVideoFormat(self, url):
        """判断是否为视频格式"""
        pass

    def manualVideoCheck(self):
        """手动视频检查"""
        pass

    def destroy(self):
        """销毁"""
        pass

    def localProxy(self, param):
        """本地代理"""
        return None
